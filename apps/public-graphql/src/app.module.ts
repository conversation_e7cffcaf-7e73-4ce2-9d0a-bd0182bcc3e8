import { Module } from '@nestjs/common';
import { MikroOrmModule } from '@mikro-orm/nestjs';
import { GraphQLModule } from '@nestjs/graphql';
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { upperDirectiveTransformer } from '../../../libs/common/directives/upper-case.directive';
import { DirectiveLocation } from 'graphql/language';
import { GraphQLDirective } from 'graphql/type';
import defineConfig from '../../../mikro-orm.config';
import { CachingModule } from '../../../libs/internal/caching/caching.module';
import { DecimalScalar } from '../../../libs/common/scalar/decimal';
import { RedisModule } from '@lib/redis';
import { ResolversModule } from './resolvers/resolvers.module';
import { LoggerModule } from '@libs/logger';
import { NatsModule } from 'libs/internal/nats/nats.module';
import { SentryModule } from '@libs/sentry';
import { HealthzModule } from 'libs/healthz/src';
import { TerminusModule } from '@nestjs/terminus';
import { HealthzController } from './healthz/healthz.controller';
import { AuthController } from './auth/auth.controller';
import { OidcDiscoveryController } from './oidc-discovery/oidc-discovery.controller';

@Module({
    imports: [
        LoggerModule.forRootAsync('user_public_graphql'),
        RedisModule.registerAsync(),
        MikroOrmModule.forRoot(defineConfig),
        GraphQLModule.forRoot<ApolloDriverConfig>({
            driver: ApolloDriver,
            subscriptions: {
                'graphql-ws': true,
            },
            autoSchemaFile: './apps/public-graphql/src/schema.gql',
            path: '/api/user/graphql',
            transformSchema: (schema) => upperDirectiveTransformer(schema, 'upper'),
            buildSchemaOptions: {
                directives: [
                    new GraphQLDirective({
                        name: 'upper',
                        locations: [DirectiveLocation.FIELD_DEFINITION],
                    }),
                ],
            },
            context: (ctx) => ctx,
            formatError: (error) => {
                return {
                    message: error.message,
                    path: error.path,
                    extensions: {
                        code: error.extensions?.code || 'INTERNAL_SERVER_ERROR',
                    },
                };
            },
        }),
        ResolversModule,
        CachingModule,
        NatsModule.registerAsync(),
        SentryModule.register(),
        TerminusModule,
        HealthzModule,
    ],
    controllers: [HealthzController, AuthController, OidcDiscoveryController],
    providers: [DecimalScalar],
})
export class AppModule {}
