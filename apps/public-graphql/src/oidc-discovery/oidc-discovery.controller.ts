import { Controller, Get } from '@nestjs/common';

@Controller('api/user/.well-known')
export class OidcDiscoveryController {
    @Get('openid-configuration')
    getOpenIdConfig() {
        const baseUrl = 'https://unstable-api.xbit.live/api/user'; // use env var in real code

        return {
            issuer: baseUrl,
            authorization_endpoint: `${baseUrl}/auth/authorize`,
            token_endpoint: `${baseUrl}/auth/token`,
            jwks_uri: `${baseUrl}/auth/keys`,
            response_types_supported: ['code', 'id_token', 'token id_token'],
            subject_types_supported: ['public'],
            id_token_signing_alg_values_supported: ['RS256'],
            scopes_supported: ['openid', 'profile', 'email'],
            token_endpoint_auth_methods_supported: ['client_secret_basic'],
        };
    }
}
