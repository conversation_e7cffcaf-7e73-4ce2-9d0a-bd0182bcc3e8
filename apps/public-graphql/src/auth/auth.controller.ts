import { Controller, Get } from '@nestjs/common';

@Controller('api/user/auth')
export class AuthController {
    @Get('keys')
    async getKeys() {
        return {
            keys: [
                {
                    kty: 'RSA',
                    n: '0_8ZEDeq2LYUra59JAMxbTujn9nqip3OPkqdNiaKQmRmWM9gbqNkr5betbFzbKBuducmEPzu_Y8fD1h-JWb1lj8lB5-e6VUvWXPdGItY9ClohL3NPsJCFC3Th4Ok24bpeSmS7x7Xbov0SjADZBpaMwzKCJkC4MIY64FQN53HwvbinG7jBBKAvVRikn7PMoGGfGDb9XKDGa4rcTzJaqpB8-lwQxZ3D9lH0pULkiWV_yVFiyfZuUpizL4LIOu3NCdBoXFTr-SQPpXyK4z7kzZwAUNv8UHz6Sn-1d3rUijLGKe0EBEe3kTAy1SbMndMBCv6zUNJX4Ljk3PgZPDVjfF-gQ',
                    e: 'AQAB',
                    kid: 'lUkwbCsQFm',
                    use: 'sig',
                    alg: 'RS256',
                },
            ],
        };
    }
}
